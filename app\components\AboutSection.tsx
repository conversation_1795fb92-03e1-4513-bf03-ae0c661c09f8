'use client';

import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const stats = [
  { number: '25+', label: 'Years of Experience', icon: '🌱' },
  { number: '500+', label: 'Mango Trees', icon: '🥭' },
  { number: '100%', label: 'Organic Certified', icon: '🌿' },
  { number: '1000+', label: 'Happy Customers', icon: '😊' }
];

const features = [
  {
    title: 'Sustainable Farming',
    description: 'We use eco-friendly practices that preserve soil health and biodiversity.',
    icon: '🌍'
  },
  {
    title: 'Premium Quality',
    description: 'Hand-picked mangoes at perfect ripeness for maximum sweetness and flavor.',
    icon: '⭐'
  },
  {
    title: 'Farm Fresh',
    description: 'Direct from our farm to your table, ensuring freshness and quality.',
    icon: '🚚'
  }
];

export default function AboutSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!sectionRef.current) return;

    const ctx = gsap.context(() => {
      // Animate statistics counters
      const statNumbers = statsRef.current?.querySelectorAll('.stat-number');
      statNumbers?.forEach((stat) => {
        const finalNumber = stat.textContent?.replace(/\D/g, '') || '0';
        const suffix = stat.textContent?.replace(/\d/g, '') || '';
        
        gsap.fromTo(stat, 
          { textContent: '0' + suffix },
          {
            textContent: finalNumber + suffix,
            duration: 2,
            ease: 'power2.out',
            snap: { textContent: 1 },
            scrollTrigger: {
              trigger: stat,
              start: 'top 80%',
              toggleActions: 'play none none reverse'
            }
          }
        );
      });

      // Animate feature cards
      gsap.fromTo('.feature-card',
        { 
          opacity: 0, 
          y: 50,
          rotateX: -15
        },
        {
          opacity: 1,
          y: 0,
          rotateX: 0,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.features-grid',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Parallax background elements
      gsap.to('.about-bg-element', {
        yPercent: -30,
        ease: 'none',
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top bottom',
          end: 'bottom top',
          scrub: true
        }
      });

    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section ref={sectionRef} className="py-20 relative overflow-hidden bg-gradient-to-b from-green-50 to-white">
      {/* Background Elements */}
      <div className="about-bg-element absolute top-20 right-10 w-64 h-64 bg-green-200 rounded-full opacity-20 blur-3xl"></div>
      <div className="about-bg-element absolute bottom-20 left-10 w-48 h-48 bg-orange-200 rounded-full opacity-20 blur-2xl"></div>

      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16 gsap-fade-in">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            <span className="text-green-700">About Our</span>
            <span className="text-transparent bg-clip-text mango-gradient"> Heritage Farm</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            For over two decades, Buppi Mango & Agri Farm has been cultivating the finest organic mangoes 
            using traditional wisdom combined with modern sustainable practices.
          </p>
        </div>

        {/* Statistics */}
        <div ref={statsRef} className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => (
            <div 
              key={index}
              className="text-center gsap-scale-in perspective-1000"
            >
              <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 preserve-3d">
                <div className="text-4xl mb-4">{stat.icon}</div>
                <div className="stat-number text-3xl lg:text-4xl font-bold text-orange-500 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Features Grid */}
        <div className="features-grid grid lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="feature-card bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 preserve-3d"
            >
              <div className="text-5xl mb-6 text-center">{feature.icon}</div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-center leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Story Section */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="gsap-slide-left">
            <h3 className="text-3xl font-bold text-gray-800 mb-6">Our Story</h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Started by a passionate farmer with a vision to grow the world's best mangoes, 
              Buppi Farm has grown from a small family operation to a renowned organic farm. 
              We believe in nurturing the land that nurtures us.
            </p>
            <p className="text-gray-600 mb-8 leading-relaxed">
              Every mango tree on our farm is treated with care and respect, following 
              sustainable practices that have been passed down through generations while 
              embracing modern organic farming techniques.
            </p>
            <button className="green-gradient text-white px-8 py-4 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
              Learn More About Us
            </button>
          </div>
          
          <div className="gsap-slide-right">
            <div className="relative">
              <div className="bg-gradient-to-br from-orange-100 to-green-100 rounded-3xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <div className="text-center">
                    <div className="text-6xl mb-4">🌳</div>
                    <h4 className="text-2xl font-bold text-gray-800 mb-4">Farm Vision</h4>
                    <p className="text-gray-600">
                      "To create a sustainable ecosystem where nature and agriculture 
                      work in harmony, producing the finest mangoes while preserving 
                      our environment for future generations."
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
