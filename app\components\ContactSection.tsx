'use client';

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';

export default function ContactSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: ''
  });

  useEffect(() => {
    if (!sectionRef.current) return;

    const ctx = gsap.context(() => {
      // Animate contact cards
      gsap.fromTo('.contact-card',
        { 
          opacity: 0, 
          y: 50,
          rotateX: -10
        },
        {
          opacity: 1,
          y: 0,
          rotateX: 0,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.contact-grid',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Animate form
      gsap.fromTo('.contact-form',
        { opacity: 0, x: 50 },
        {
          opacity: 1,
          x: 0,
          duration: 1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.contact-form',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
    
    // Animate success
    gsap.to('.submit-btn', {
      scale: 0.95,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: 'power2.inOut'
    });
  };

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-b from-green-50 to-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-orange-200 rounded-full opacity-20 blur-2xl"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-green-200 rounded-full opacity-20 blur-3xl"></div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 gsap-fade-in">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            <span className="text-green-700">Get In</span>
            <span className="text-transparent bg-clip-text mango-gradient"> Touch</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Ready to experience the finest organic mangoes? Contact us for orders, 
            farm visits, or any questions about our sustainable farming practices.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div>
            <div className="contact-grid space-y-6 mb-8">
              <div className="contact-card bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 preserve-3d">
                <div className="flex items-center">
                  <div className="text-3xl mr-4">📍</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-1">Farm Location</h3>
                    <p className="text-gray-600">Buppi Village, Agricultural District</p>
                    <p className="text-gray-600">Mango Growing Region, India</p>
                  </div>
                </div>
              </div>

              <div className="contact-card bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 preserve-3d">
                <div className="flex items-center">
                  <div className="text-3xl mr-4">📞</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-1">Phone</h3>
                    <p className="text-gray-600">+91 98765 43210</p>
                    <p className="text-gray-600">+91 87654 32109</p>
                  </div>
                </div>
              </div>

              <div className="contact-card bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 preserve-3d">
                <div className="flex items-center">
                  <div className="text-3xl mr-4">✉️</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-1">Email</h3>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>
              </div>

              <div className="contact-card bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 preserve-3d">
                <div className="flex items-center">
                  <div className="text-3xl mr-4">🕒</div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-1">Farm Hours</h3>
                    <p className="text-gray-600">Monday - Saturday: 6:00 AM - 6:00 PM</p>
                    <p className="text-gray-600">Sunday: 7:00 AM - 4:00 PM</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Social Media */}
            <div className="contact-card bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="font-semibold text-gray-800 mb-4 text-center">Follow Our Journey</h3>
              <div className="flex justify-center space-x-4">
                <a href="#" className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300">
                  📘
                </a>
                <a href="#" className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300">
                  📷
                </a>
                <a href="#" className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300">
                  💬
                </a>
                <a href="#" className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300">
                  📺
                </a>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="contact-form">
            <div className="bg-white rounded-3xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                Send Us a Message
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-700 font-medium mb-2">Name</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-300 focus:border-transparent transition-all duration-300"
                    placeholder="Your full name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">Email</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-300 focus:border-transparent transition-all duration-300"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">Phone</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-300 focus:border-transparent transition-all duration-300"
                    placeholder="+91 98765 43210"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">Message</label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-300 focus:border-transparent transition-all duration-300 resize-none"
                    placeholder="Tell us about your requirements..."
                    required
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="submit-btn w-full mango-gradient text-white py-4 rounded-lg font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300 pulse-glow"
                >
                  Send Message 🥭
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
