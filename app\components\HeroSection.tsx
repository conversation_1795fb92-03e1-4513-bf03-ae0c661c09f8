'use client';

import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import SimpleMango from './SimpleMango';

export default function HeroSection() {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!heroRef.current) return;

    const ctx = gsap.context(() => {
      // Hero entrance animation
      const tl = gsap.timeline();
      
      tl.fromTo(titleRef.current,
        { opacity: 0, y: 100, scale: 0.8 },
        { opacity: 1, y: 0, scale: 1, duration: 1.2, ease: 'power3.out' }
      )
      .fromTo(subtitleRef.current,
        { opacity: 0, y: 50 },
        { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' },
        '-=0.6'
      )
      .fromTo(ctaRef.current,
        { opacity: 0, y: 30 },
        { opacity: 1, y: 0, duration: 0.6, ease: 'power2.out' },
        '-=0.4'
      );

      // Floating animation for background elements
      gsap.to('.floating-mango', {
        y: -20,
        rotation: 5,
        duration: 3,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });

      // Parallax effect on scroll
      gsap.to('.hero-bg', {
        yPercent: -50,
        ease: 'none',
        scrollTrigger: {
          trigger: heroRef.current,
          start: 'top top',
          end: 'bottom top',
          scrub: true
        }
      });

    }, heroRef);

    return () => ctx.revert();
  }, []);

  return (
    <section 
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-orange-50 via-yellow-50 to-green-50"
    >
      {/* Background Pattern */}
      <div className="hero-bg absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-32 h-32 rounded-full bg-orange-300 blur-xl"></div>
        <div className="absolute top-40 right-32 w-24 h-24 rounded-full bg-yellow-300 blur-lg"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 rounded-full bg-green-300 blur-2xl"></div>
        <div className="absolute bottom-20 right-20 w-28 h-28 rounded-full bg-orange-400 blur-lg"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <div className="text-center lg:text-left">
            <h1 
              ref={titleRef}
              className="text-5xl lg:text-7xl font-bold mb-6 leading-tight"
            >
              <span className="text-transparent bg-clip-text mango-gradient">
                Buppi Mango
              </span>
              <br />
              <span className="text-green-700">& Agri Farm</span>
            </h1>
            
            <p 
              ref={subtitleRef}
              className="text-xl lg:text-2xl text-gray-700 mb-8 max-w-2xl"
            >
              Experience the sweetest, most authentic organic mangoes grown with 
              <span className="text-orange-500 font-semibold"> sustainable farming practices</span> 
              and generations of agricultural wisdom.
            </p>
            
            <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <button className="px-8 py-4 mango-gradient text-white font-semibold rounded-full hover:shadow-lg transform hover:scale-105 transition-all duration-300 pulse-glow">
                Explore Our Farm
              </button>
              <button className="px-8 py-4 border-2 border-green-600 text-green-600 font-semibold rounded-full hover:bg-green-600 hover:text-white transition-all duration-300">
                Shop Mangoes
              </button>
            </div>
          </div>

          {/* Animated Mango */}
          <div className="relative">
            <div className="floating-mango w-full h-96 lg:h-[500px]">
              <SimpleMango className="w-full h-full" />
            </div>
            
            {/* Decorative Elements */}
            <div className="absolute -top-10 -right-10 w-20 h-20 bg-yellow-300 rounded-full opacity-60 float-animation"></div>
            <div className="absolute -bottom-5 -left-5 w-16 h-16 bg-orange-300 rounded-full opacity-50 float-animation" style={{ animationDelay: '1s' }}></div>
            <div className="absolute top-1/2 -right-8 w-12 h-12 bg-green-300 rounded-full opacity-40 float-animation" style={{ animationDelay: '2s' }}></div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-orange-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-orange-400 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
