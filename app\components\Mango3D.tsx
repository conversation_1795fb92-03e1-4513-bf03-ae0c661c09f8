'use client';

import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Mango3D({ className = "" }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const mangoRef = useRef<HTMLDivElement>(null);
  const leafRef1 = useRef<HTMLDivElement>(null);
  const leafRef2 = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<HTMLDivElement>(null);
  const glowRef = useRef<HTMLDivElement>(null);
  const orbitRef1 = useRef<HTMLDivElement>(null);
  const orbitRef2 = useRef<HTMLDivElement>(null);
  const orbitRef3 = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const ctx = gsap.context(() => {
      // 🔥 COOL ANIMATION 1: Epic 3D Rotation with Morphing
      gsap.to(mangoRef.current, {
        rotationY: 360,
        rotationX: 15,
        duration: 6,
        ease: 'power1.inOut',
        repeat: -1,
        yoyo: true
      });

      // 🌊 COOL ANIMATION 2: Magnetic Floating with Physics
      gsap.to(mangoRef.current, {
        y: -30,
        x: 10,
        duration: 4,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1
      });

      // ⚡ COOL ANIMATION 3: Scroll-Triggered Mega Spin
      gsap.fromTo(mangoRef.current,
        {
          rotationZ: 0,
          scale: 0.8
        },
        {
          rotationZ: 540, // 1.5 full rotations!
          scale: 1.5,
          duration: 3,
          ease: 'elastic.out(1, 0.5)',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 85%',
            end: 'bottom 15%',
            scrub: 2,
            onEnter: () => {
              gsap.to(glowRef.current, {
                scale: 2,
                opacity: 0.8,
                duration: 0.5
              });
            },
            onLeave: () => {
              gsap.to(glowRef.current, {
                scale: 1,
                opacity: 0.3,
                duration: 0.5
              });
            }
          }
        }
      );

      // 🚀 COOL ANIMATION 4: Rocket Launch Movement
      gsap.fromTo(containerRef.current,
        { x: -window.innerWidth * 0.3, y: 50 },
        {
          x: window.innerWidth * 0.3,
          y: -50,
          duration: 4,
          ease: 'power3.inOut',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 90%',
            end: 'bottom 10%',
            scrub: 1.5,
            onUpdate: (self) => {
              // Cool wobble effect with sine wave
              const wobble = Math.sin(self.progress * Math.PI * 6) * 20;
              gsap.set(mangoRef.current, {
                rotationX: wobble,
                rotationZ: wobble * 0.5
              });
            }
          }
        }
      );

      // 🌟 COOL ANIMATION 5: Orbiting Elements
      gsap.to(orbitRef1.current, {
        rotation: 360,
        duration: 8,
        ease: 'none',
        repeat: -1,
        transformOrigin: '100px 0px'
      });

      gsap.to(orbitRef2.current, {
        rotation: -360,
        duration: 12,
        ease: 'none',
        repeat: -1,
        transformOrigin: '80px 0px'
      });

      gsap.to(orbitRef3.current, {
        rotation: 360,
        duration: 15,
        ease: 'none',
        repeat: -1,
        transformOrigin: '120px 0px'
      });

      // 🍃 COOL ANIMATION 6: Dynamic Leaves
      gsap.to(leafRef1.current, {
        rotation: 720,
        scale: 1.2,
        duration: 3,
        ease: 'back.inOut(1.7)',
        scrollTrigger: {
          trigger: containerRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          scrub: 1
        }
      });

      gsap.to(leafRef2.current, {
        rotation: -540,
        scale: 0.8,
        duration: 3,
        ease: 'elastic.out(1, 0.3)',
        scrollTrigger: {
          trigger: containerRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          scrub: 1.5
        }
      });

      // 💫 COOL ANIMATION 7: Pulsing Glow
      gsap.to(glowRef.current, {
        scale: 1.5,
        opacity: 0.6,
        duration: 2,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1
      });

      // 🌑 COOL ANIMATION 8: Dynamic Shadow
      gsap.to(shadowRef.current, {
        scaleX: 1.8,
        scaleY: 0.6,
        opacity: 0.4,
        duration: 4,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1
      });

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <div className={`w-full h-full min-h-[400px] lg:min-h-[600px] relative overflow-hidden ${className}`}>
      {/* Main Mango Container - Better Sizing */}
      <div ref={containerRef} className="flex items-center justify-center perspective-1000 h-full w-full">
        <div className="relative preserve-3d w-fit h-fit">

          {/* 🌑 Dynamic Shadow */}
          <div
            ref={shadowRef}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-12 w-40 h-12 opacity-30 rounded-full blur-md"
            style={{
              background: 'radial-gradient(ellipse, rgba(255,140,66,0.4) 0%, rgba(0,0,0,0.2) 50%, transparent 100%)'
            }}
          ></div>

          {/* 💫 Epic Glow Effect */}
          <div
            ref={glowRef}
            className="absolute inset-0 rounded-full opacity-30 blur-2xl scale-150"
            style={{
              background: 'radial-gradient(circle, rgba(255,140,66,0.6) 0%, rgba(255,180,70,0.4) 30%, rgba(255,200,100,0.2) 60%, transparent 100%)'
            }}
          ></div>

          {/* 🥭 Main Epic Mango - Perfect Size */}
          <div
            ref={mangoRef}
            className="relative text-[120px] sm:text-[150px] md:text-[180px] lg:text-[220px] xl:text-[280px] preserve-3d transform-gpu leading-none"
            style={{
              filter: 'drop-shadow(0 20px 40px rgba(255, 140, 66, 0.5)) drop-shadow(0 0 60px rgba(255, 140, 66, 0.3))',
              textShadow: '0 0 50px rgba(255, 140, 66, 0.8), 0 0 100px rgba(255, 140, 66, 0.4), 0 0 150px rgba(255, 140, 66, 0.2)'
            }}
          >
            🥭
          </div>

          {/* 🍃 Super Cool Leaves */}
          <div
            ref={leafRef1}
            className="absolute -top-6 -right-4 text-[40px] sm:text-[50px] md:text-[60px] lg:text-[70px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 10px 20px rgba(34, 139, 34, 0.5))',
              textShadow: '0 0 30px rgba(34, 139, 34, 0.7), 0 0 60px rgba(34, 139, 34, 0.3)'
            }}
          >
            🌿
          </div>

          <div
            ref={leafRef2}
            className="absolute -top-3 -right-8 text-[30px] sm:text-[35px] md:text-[40px] lg:text-[50px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 8px 16px rgba(34, 139, 34, 0.5))',
              textShadow: '0 0 25px rgba(34, 139, 34, 0.7), 0 0 50px rgba(34, 139, 34, 0.3)'
            }}
          >
            🍃
          </div>

          {/* 🌟 Orbiting Elements */}
          <div
            ref={orbitRef1}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div className="w-2 h-2 bg-yellow-400 rounded-full shadow-lg" style={{ transform: 'translateX(100px)' }}></div>
          </div>

          <div
            ref={orbitRef2}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div className="w-3 h-3 bg-orange-400 rounded-full shadow-lg" style={{ transform: 'translateX(80px)' }}></div>
          </div>

          <div
            ref={orbitRef3}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div className="w-1.5 h-1.5 bg-green-400 rounded-full shadow-lg" style={{ transform: 'translateX(120px)' }}></div>
          </div>
        </div>
      </div>

      {/* 🎆 Epic Particle System */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full opacity-70"
            style={{
              width: `${4 + (i % 3) * 2}px`,
              height: `${4 + (i % 3) * 2}px`,
              background: `radial-gradient(circle, ${
                ['#FFD700', '#FFA500', '#FF8C42', '#32CD32', '#228B22'][i % 5]
              }, transparent)`,
              left: `${10 + (i * 7) % 80}%`,
              top: `${20 + (i * 11) % 60}%`,
              animation: `float ${3 + (i % 4)}s ease-in-out infinite`,
              animationDelay: `${i * 0.3}s`
            }}
          ></div>
        ))}
      </div>

      {/* 🌈 Rainbow Trail Effect */}
      <div className="absolute inset-0 pointer-events-none">
        <div
          className="w-full h-2 absolute top-1/2 transform -translate-y-1/2 opacity-40"
          style={{
            background: 'linear-gradient(90deg, transparent 0%, #FFD700 20%, #FFA500 40%, #FF8C42 60%, #32CD32 80%, transparent 100%)'
          }}
        ></div>
      </div>

      {/* 💎 Corner Decorations */}
      <div className="absolute top-4 left-4 text-2xl opacity-60 animate-pulse">✨</div>
      <div className="absolute top-4 right-4 text-2xl opacity-60 animate-pulse" style={{ animationDelay: '0.5s' }}>⭐</div>
      <div className="absolute bottom-4 left-4 text-2xl opacity-60 animate-pulse" style={{ animationDelay: '1s' }}>🌟</div>
      <div className="absolute bottom-4 right-4 text-2xl opacity-60 animate-pulse" style={{ animationDelay: '1.5s' }}>💫</div>
    </div>
  );
}
