'use client';

import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Mango3D({ className = "" }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const mangoRef = useRef<HTMLDivElement>(null);
  const leafRef1 = useRef<HTMLDivElement>(null);
  const leafRef2 = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<HTMLDivElement>(null);
  const glowRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const ctx = gsap.context(() => {
      // 🥭 CORRECTED ORIENTATION: Set mango to face correctly (90° down)
      gsap.set(mangoRef.current, {
        rotationX: -90,
        transformOrigin: 'center center',
        transformStyle: 'preserve-3d'
      });

      // 🎯 SIMPLE ANIMATION 1: Gentle 3D Rotation
      gsap.to(mangoRef.current, {
        rotationY: 360,
        duration: 12,
        ease: 'none',
        repeat: -1
      });

      // 🌊 SIMPLE ANIMATION 2: Soft Float
      gsap.to(mangoRef.current, {
        y: -20,
        duration: 4,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1
      });

      // 🔄 SIMPLE ANIMATION 3: 180° Horizontal Rotation on Scroll
      gsap.fromTo(mangoRef.current, 
        { 
          rotationY: 0,
          scale: 1
        },
        {
          rotationY: 180,
          scale: 1.3,
          duration: 3,
          ease: 'power2.inOut',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            scrub: 2
          }
        }
      );

      // ↔️ SIMPLE ANIMATION 4: Left to Right Movement
      gsap.fromTo(containerRef.current,
        { x: -150 },
        {
          x: 150,
          duration: 4,
          ease: 'power2.inOut',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 85%',
            end: 'bottom 15%',
            scrub: 1.5
          }
        }
      );

      // ⬇️ SIMPLE ANIMATION 5: Vertical Down Movement
      gsap.fromTo(containerRef.current,
        { y: -60 },
        {
          y: 60,
          duration: 3,
          ease: 'power1.inOut',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 90%',
            end: 'bottom 10%',
            scrub: 1
          }
        }
      );

      // 🍃 SIMPLE ANIMATION 6: Gentle Leaves
      gsap.to(leafRef1.current, {
        rotation: 180,
        scale: 1.1,
        duration: 6,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1
      });

      gsap.to(leafRef2.current, {
        rotation: -120,
        scale: 0.9,
        duration: 8,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1
      });

      // 💫 SIMPLE ANIMATION 7: Soft Glow
      gsap.to(glowRef.current, {
        scale: 1.5,
        opacity: 0.4,
        duration: 5,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });

      // 🌑 SIMPLE ANIMATION 8: Gentle Shadow
      gsap.to(shadowRef.current, {
        scaleX: 1.3,
        opacity: 0.2,
        duration: 4,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1
      });

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <div className={`w-full h-full min-h-[400px] lg:min-h-[600px] relative overflow-hidden ${className}`}>
      {/* Main Container */}
      <div ref={containerRef} className="flex items-center justify-center perspective-1000 h-full w-full">
        <div className="relative preserve-3d w-fit h-fit">
          
          {/* 🌑 Simple Shadow */}
          <div
            ref={shadowRef}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-12 w-32 h-8 opacity-20 rounded-full blur-sm"
            style={{
              background: 'linear-gradient(ellipse, rgba(255,140,66,0.4) 0%, rgba(0,0,0,0.2) 70%, transparent 100%)'
            }}
          ></div>

          {/* 💫 Simple Glow */}
          <div
            ref={glowRef}
            className="absolute inset-0 rounded-full opacity-25 blur-xl scale-125"
            style={{
              background: 'radial-gradient(circle, rgba(255,140,66,0.5) 0%, rgba(255,180,70,0.3) 50%, transparent 80%)'
            }}
          ></div>

          {/* 🥭 Simple 3D Mango */}
          <div
            ref={mangoRef}
            className="relative text-[120px] sm:text-[150px] md:text-[180px] lg:text-[220px] xl:text-[280px] preserve-3d transform-gpu leading-none"
            style={{
              filter: 'drop-shadow(0 15px 25px rgba(255, 140, 66, 0.3))',
              textShadow: '0 0 30px rgba(255, 140, 66, 0.5)'
            }}
          >
            🥭
          </div>

          {/* 🍃 Simple Leaves */}
          <div
            ref={leafRef1}
            className="absolute -top-4 -right-3 text-[35px] sm:text-[40px] md:text-[45px] lg:text-[50px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 5px 10px rgba(34, 139, 34, 0.4))',
              textShadow: '0 0 15px rgba(34, 139, 34, 0.5)'
            }}
          >
            🌿
          </div>

          <div
            ref={leafRef2}
            className="absolute -top-2 -right-6 text-[25px] sm:text-[30px] md:text-[35px] lg:text-[40px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 4px 8px rgba(34, 139, 34, 0.4))',
              textShadow: '0 0 12px rgba(34, 139, 34, 0.5)'
            }}
          >
            🍃
          </div>
        </div>
      </div>

      {/* ✨ Simple Particles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full opacity-40"
            style={{
              width: `${3 + (i % 2) * 2}px`,
              height: `${3 + (i % 2) * 2}px`,
              background: `linear-gradient(circle, ${
                ['#FFD700', '#FFA500', '#FF8C42'][i % 3]
              }, transparent)`,
              left: `${25 + (i * 12) % 50}%`,
              top: `${35 + (i * 8) % 30}%`,
              animation: `float ${5 + (i % 2)}s ease-in-out infinite`,
              animationDelay: `${i * 0.8}s`
            }}
          ></div>
        ))}
      </div>

      {/* 🌟 Simple Corner Effects */}
      <div className="absolute top-4 left-4 text-xl opacity-30 animate-pulse">✨</div>
      <div className="absolute top-4 right-4 text-xl opacity-30 animate-pulse" style={{ animationDelay: '1s' }}>⭐</div>
      <div className="absolute bottom-4 left-4 text-xl opacity-30 animate-pulse" style={{ animationDelay: '2s' }}>🌟</div>
      <div className="absolute bottom-4 right-4 text-xl opacity-30 animate-pulse" style={{ animationDelay: '3s' }}>💫</div>
    </div>
  );
}
