'use client';

import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';

export default function Mango3D({ className = "" }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const mangoRef = useRef<HTMLDivElement>(null);
  const leafRef1 = useRef<HTMLDivElement>(null);
  const leafRef2 = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const ctx = gsap.context(() => {
      // Main mango 3D rotation and floating
      gsap.to(mangoRef.current, {
        rotationY: 360,
        duration: 8,
        ease: 'none',
        repeat: -1
      });

      gsap.to(mangoRef.current, {
        y: -20,
        duration: 3,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });

      // 3D wobble effect
      gsap.to(mangoRef.current, {
        rotationX: 10,
        duration: 4,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });

      // Leaves animation
      gsap.to(leafRef1.current, {
        rotation: 15,
        duration: 2,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });

      gsap.to(leafRef2.current, {
        rotation: -10,
        duration: 2.5,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1,
        delay: 0.5
      });

      // Shadow animation
      gsap.to(shadowRef.current, {
        scale: 1.1,
        opacity: 0.3,
        duration: 3,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <div ref={containerRef} className={`w-full h-full flex items-center justify-center perspective-1000 ${className}`}>
      <div className="relative preserve-3d">
        {/* Shadow */}
        <div
          ref={shadowRef}
          className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-8 w-32 h-8 bg-black opacity-20 rounded-full blur-sm"
        ></div>

        {/* Main Mango */}
        <div
          ref={mangoRef}
          className="relative text-8xl lg:text-9xl preserve-3d"
          style={{
            filter: 'drop-shadow(0 10px 20px rgba(255, 140, 66, 0.3))',
            textShadow: '0 0 30px rgba(255, 140, 66, 0.5)'
          }}
        >
          🥭
        </div>

        {/* Leaves */}
        <div
          ref={leafRef1}
          className="absolute -top-4 -right-2 text-3xl transform-gpu"
          style={{
            filter: 'drop-shadow(0 5px 10px rgba(34, 139, 34, 0.3))'
          }}
        >
          🌿
        </div>

        <div
          ref={leafRef2}
          className="absolute -top-2 -right-6 text-2xl transform-gpu"
          style={{
            filter: 'drop-shadow(0 5px 10px rgba(34, 139, 34, 0.3))'
          }}
        >
          🍃
        </div>

        {/* Glow effect */}
        <div className="absolute inset-0 rounded-full bg-gradient-radial from-orange-300/20 to-transparent blur-xl scale-150"></div>
      </div>
    </div>
  );
}
