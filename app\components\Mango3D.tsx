'use client';

import { useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Sphere, MeshWobbleMaterial } from '@react-three/drei';
import * as THREE from 'three';

function MangoMesh() {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5;
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.2;
    }
  });

  return (
    <Sphere ref={meshRef} args={[1, 32, 32]} scale={[1.2, 1.5, 1]}>
      <MeshWobbleMaterial
        color="#FF8C42"
        attach="material"
        factor={0.1}
        speed={2}
        roughness={0.1}
        metalness={0.1}
      />
    </Sphere>
  );
}

function MangoLeaf({ position }: { position: [number, number, number] }) {
  const leafRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (leafRef.current) {
      leafRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  return (
    <mesh ref={leafRef} position={position} rotation={[0, 0, Math.PI / 6]}>
      <planeGeometry args={[0.5, 1]} />
      <meshStandardMaterial color="#228B22" side={THREE.DoubleSide} />
    </mesh>
  );
}

export default function Mango3D({ className = "" }: { className?: string }) {
  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas camera={{ position: [0, 0, 5], fov: 45 }}>
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <pointLight position={[-10, -10, -5]} intensity={0.5} color="#FFB347" />
        
        <MangoMesh />
        <MangoLeaf position={[0.8, 1.2, 0]} />
        <MangoLeaf position={[0.6, 1.3, 0.2]} />
        
        <OrbitControls 
          enableZoom={false} 
          enablePan={false}
          autoRotate
          autoRotateSpeed={1}
        />
      </Canvas>
    </div>
  );
}
