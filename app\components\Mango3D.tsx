'use client';

import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Mango3D({ className = "" }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const mangoRef = useRef<HTMLDivElement>(null);
  const leafRef1 = useRef<HTMLDivElement>(null);
  const leafRef2 = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<HTMLDivElement>(null);
  const glowRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const ctx = gsap.context(() => {
      // 🥭 CORRECTED ORIENTATION: Set mango to face correctly (90° down)
      gsap.set(mangoRef.current, {
        rotationX: -90, // Face down correctly
        transformOrigin: 'center center',
        transformStyle: 'preserve-3d'
      });

      // 🎯 NEW ANIMATION 1: Perfect 3D Pendulum Swing
      gsap.to(mangoRef.current, {
        rotationY: 45,
        rotationZ: 30,
        duration: 4,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        transformOrigin: 'center top'
      });

      // 🌊 NEW ANIMATION 2: Gentle Wave Float
      gsap.to(mangoRef.current, {
        y: -25,
        x: 15,
        duration: 6,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1,
        onUpdate: function() {
          // Subtle breathing effect
          const progress = this.progress();
          const breathe = Math.sin(progress * Math.PI * 2) * 5;
          gsap.set(mangoRef.current, { 
            scale: 1 + (breathe * 0.02),
            rotationX: -90 + breathe
          });
        }
      });

      // 🔄 NEW ANIMATION 3: Smooth 180° Horizontal Rotation on Scroll
      gsap.fromTo(mangoRef.current, 
        { 
          rotationY: 0,
          scale: 1
        },
        {
          rotationY: 180, // Perfect 180° horizontal rotation
          scale: 1.4,
          duration: 3,
          ease: 'power3.inOut',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            scrub: 2,
            onEnter: () => {
              gsap.to(glowRef.current, {
                scale: 2,
                opacity: 0.8,
                duration: 0.6
              });
            },
            onLeave: () => {
              gsap.to(glowRef.current, {
                scale: 1,
                opacity: 0.4,
                duration: 0.6
              });
            }
          }
        }
      );

      // ↔️ NEW ANIMATION 4: Smooth Left to Right Movement
      gsap.fromTo(containerRef.current,
        { x: -200 },
        {
          x: 200,
          duration: 4,
          ease: 'power2.inOut',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 85%',
            end: 'bottom 15%',
            scrub: 1.5,
            onUpdate: (self) => {
              // Gentle sway during movement
              const sway = Math.sin(self.progress * Math.PI * 3) * 10;
              gsap.set(mangoRef.current, {
                rotationZ: sway,
                rotationX: -90 + (sway * 0.3)
              });
            }
          }
        }
      );

      // ⬇️ NEW ANIMATION 5: Controlled Vertical Down Movement
      gsap.fromTo(containerRef.current,
        { y: -80 },
        {
          y: 80,
          duration: 3,
          ease: 'power1.inOut',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 90%',
            end: 'bottom 10%',
            scrub: 1,
            onUpdate: (self) => {
              // Gentle rotation during descent
              const descent = self.progress * 15;
              gsap.set(mangoRef.current, {
                rotationX: -90 + descent,
                rotationY: descent * 2
              });
            }
          }
        }
      );

      // 🍃 NEW ANIMATION 6: Elegant Leaves
      gsap.to(leafRef1.current, {
        rotation: 360,
        scale: 1.2,
        duration: 8,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });

      gsap.to(leafRef2.current, {
        rotation: -270,
        scale: 0.9,
        duration: 6,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1
      });

      // 💫 NEW ANIMATION 7: Smooth Glow
      gsap.to(glowRef.current, {
        scale: 1.8,
        opacity: 0.6,
        duration: 4,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });

      // 🌑 NEW ANIMATION 8: Elegant Shadow
      gsap.to(shadowRef.current, {
        scaleX: 1.5,
        scaleY: 0.8,
        opacity: 0.3,
        duration: 6,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1
      });

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <div className={`w-full h-full min-h-[400px] lg:min-h-[600px] relative overflow-hidden ${className}`}>
      {/* Main Mango Container - Perfect Sizing */}
      <div ref={containerRef} className="flex items-center justify-center perspective-1000 h-full w-full">
        <div className="relative preserve-3d w-fit h-fit">
          
          {/* 🌑 Elegant Shadow */}
          <div
            ref={shadowRef}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-12 w-40 h-12 opacity-25 rounded-full blur-md"
            style={{
              background: 'radial-gradient(ellipse, rgba(255,140,66,0.5) 0%, rgba(0,0,0,0.3) 50%, transparent 100%)'
            }}
          ></div>

          {/* 💫 Smooth Glow Effect */}
          <div
            ref={glowRef}
            className="absolute inset-0 rounded-full opacity-30 blur-2xl scale-150"
            style={{
              background: 'radial-gradient(circle, rgba(255,140,66,0.6) 0%, rgba(255,180,70,0.4) 40%, rgba(255,200,100,0.2) 70%, transparent 100%)'
            }}
          ></div>

          {/* 🥭 Perfectly Oriented Mango */}
          <div
            ref={mangoRef}
            className="relative text-[120px] sm:text-[150px] md:text-[180px] lg:text-[220px] xl:text-[280px] preserve-3d transform-gpu leading-none"
            style={{
              filter: 'drop-shadow(0 20px 40px rgba(255, 140, 66, 0.5)) drop-shadow(0 0 60px rgba(255, 140, 66, 0.3))',
              textShadow: '0 0 50px rgba(255, 140, 66, 0.8), 0 0 100px rgba(255, 140, 66, 0.4), 0 0 150px rgba(255, 140, 66, 0.2)'
            }}
          >
            🥭
          </div>

          {/* 🍃 Elegant Leaves */}
          <div
            ref={leafRef1}
            className="absolute -top-6 -right-4 text-[40px] sm:text-[50px] md:text-[60px] lg:text-[70px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 10px 20px rgba(34, 139, 34, 0.5))',
              textShadow: '0 0 30px rgba(34, 139, 34, 0.7), 0 0 60px rgba(34, 139, 34, 0.3)'
            }}
          >
            🌿
          </div>

          <div
            ref={leafRef2}
            className="absolute -top-3 -right-8 text-[30px] sm:text-[35px] md:text-[40px] lg:text-[50px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 8px 16px rgba(34, 139, 34, 0.5))',
              textShadow: '0 0 25px rgba(34, 139, 34, 0.7), 0 0 50px rgba(34, 139, 34, 0.3)'
            }}
          >
            🍃
          </div>
        </div>
      </div>

      {/* ✨ Elegant Particle Effects */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full opacity-60"
            style={{
              width: `${4 + (i % 3) * 2}px`,
              height: `${4 + (i % 3) * 2}px`,
              background: `radial-gradient(circle, ${
                ['#FFD700', '#FFA500', '#FF8C42', '#32CD32'][i % 4]
              }, transparent)`,
              left: `${20 + (i * 10) % 60}%`,
              top: `${30 + (i * 8) % 40}%`,
              animation: `float ${4 + (i % 3)}s ease-in-out infinite`,
              animationDelay: `${i * 0.5}s`
            }}
          ></div>
        ))}
      </div>

      {/* 🌟 Corner Sparkles */}
      <div className="absolute top-4 left-4 text-2xl opacity-50 animate-pulse">✨</div>
      <div className="absolute top-4 right-4 text-2xl opacity-50 animate-pulse" style={{ animationDelay: '0.5s' }}>⭐</div>
      <div className="absolute bottom-4 left-4 text-2xl opacity-50 animate-pulse" style={{ animationDelay: '1s' }}>🌟</div>
      <div className="absolute bottom-4 right-4 text-2xl opacity-50 animate-pulse" style={{ animationDelay: '1.5s' }}>💫</div>
    </div>
  );
}
