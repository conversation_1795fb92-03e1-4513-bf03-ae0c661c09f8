'use client';

import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Mango3D({ className = "" }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const mangoRef = useRef<HTMLDivElement>(null);
  const leafRef1 = useRef<HTMLDivElement>(null);
  const leafRef2 = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<HTMLDivElement>(null);
  const glowRef = useRef<HTMLDivElement>(null);
  const orbitRef1 = useRef<HTMLDivElement>(null);
  const orbitRef2 = useRef<HTMLDivElement>(null);
  const orbitRef3 = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const ctx = gsap.context(() => {
      // 🌪️ NEW ANIMATION 1: Tornado Spin with Morphing Scale
      gsap.to(mangoRef.current, {
        rotationY: 720, // Double spin!
        rotationX: 45,
        rotationZ: 180,
        scale: 1.3,
        duration: 8,
        ease: 'expo.inOut',
        repeat: -1,
        yoyo: true
      });

      // � NEW ANIMATION 2: Roller Coaster Float
      gsap.to(mangoRef.current, {
        y: -50,
        x: 25,
        duration: 5,
        ease: 'bounce.inOut',
        yoyo: true,
        repeat: -1,
        onUpdate: function() {
          // Add spiral motion
          const progress = this.progress();
          const spiral = Math.sin(progress * Math.PI * 4) * 15;
          gsap.set(mangoRef.current, { rotation: spiral });
        }
      });

      // 🌊 NEW ANIMATION 3: Tsunami Wave Scroll Effect
      gsap.fromTo(mangoRef.current,
        {
          rotationZ: -180,
          scale: 0.5,
          skewX: 0
        },
        {
          rotationZ: 900, // 2.5 full rotations!
          scale: 2,
          skewX: 15,
          duration: 4,
          ease: 'circ.out',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 90%',
            end: 'bottom 10%',
            scrub: 3,
            onEnter: () => {
              gsap.to(glowRef.current, {
                scale: 3,
                opacity: 1,
                duration: 0.8,
                ease: 'expo.out'
              });
            },
            onLeave: () => {
              gsap.to(glowRef.current, {
                scale: 1,
                opacity: 0.2,
                duration: 0.8
              });
            }
          }
        }
      );

      // 🎯 NEW ANIMATION 4: Zigzag Lightning Movement
      gsap.fromTo(containerRef.current,
        { x: -window.innerWidth * 0.4, y: 100, rotation: -45 },
        {
          x: window.innerWidth * 0.4,
          y: -100,
          rotation: 45,
          duration: 5,
          ease: 'rough({ template: none.out, strength: 2, points: 20, taper: "none", randomize: true, clamp: false})',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 95%',
            end: 'bottom 5%',
            scrub: 2,
            onUpdate: (self) => {
              // Zigzag effect with multiple sine waves
              const zigzag = Math.sin(self.progress * Math.PI * 12) * 30;
              const wobble = Math.cos(self.progress * Math.PI * 8) * 15;
              gsap.set(mangoRef.current, {
                rotationX: zigzag,
                rotationY: wobble,
                skewY: zigzag * 0.3
              });
            }
          }
        }
      );

      // � NEW ANIMATION 5: Vortex Orbiting System
      gsap.to(orbitRef1.current, {
        rotation: 720,
        scale: 1.5,
        duration: 6,
        ease: 'expo.inOut',
        repeat: -1,
        transformOrigin: '150px 0px',
        yoyo: true
      });

      gsap.to(orbitRef2.current, {
        rotation: -900,
        scale: 0.8,
        duration: 9,
        ease: 'circ.inOut',
        repeat: -1,
        transformOrigin: '120px 0px'
      });

      gsap.to(orbitRef3.current, {
        rotation: 1080,
        scale: 1.2,
        duration: 12,
        ease: 'sine.inOut',
        repeat: -1,
        transformOrigin: '180px 0px',
        yoyo: true
      });

      // �️ NEW ANIMATION 6: Tornado Leaves
      gsap.to(leafRef1.current, {
        rotation: 1440, // 4 full spins!
        scale: 2,
        x: 20,
        y: -10,
        duration: 4,
        ease: 'expo.inOut',
        scrollTrigger: {
          trigger: containerRef.current,
          start: 'top 85%',
          end: 'bottom 15%',
          scrub: 2,
          onUpdate: (self) => {
            const spiral = Math.sin(self.progress * Math.PI * 6) * 10;
            gsap.set(leafRef1.current, { skewX: spiral });
          }
        }
      });

      gsap.to(leafRef2.current, {
        rotation: -1080, // 3 full spins reverse!
        scale: 0.5,
        x: -15,
        y: 15,
        duration: 4,
        ease: 'bounce.out',
        scrollTrigger: {
          trigger: containerRef.current,
          start: 'top 85%',
          end: 'bottom 15%',
          scrub: 2.5
        }
      });

      // 🌈 NEW ANIMATION 7: Rainbow Pulse Glow
      gsap.to(glowRef.current, {
        scale: 2.5,
        opacity: 0.8,
        duration: 3,
        ease: 'expo.inOut',
        yoyo: true,
        repeat: -1,
        onUpdate: function() {
          // Color cycling effect
          const hue = (this.progress() * 360) % 360;
          gsap.set(glowRef.current, {
            filter: `hue-rotate(${hue}deg) brightness(1.5)`
          });
        }
      });

      // � NEW ANIMATION 8: Liquid Shadow Morph
      gsap.to(shadowRef.current, {
        scaleX: 3,
        scaleY: 0.3,
        opacity: 0.6,
        skewX: 20,
        duration: 6,
        ease: 'elastic.inOut(1, 0.3)',
        yoyo: true,
        repeat: -1,
        onUpdate: function() {
          // Liquid morphing effect
          const morph = Math.sin(this.progress() * Math.PI * 3) * 10;
          gsap.set(shadowRef.current, { borderRadius: `${50 + morph}%` });
        }
      });

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <div className={`w-full h-full min-h-[400px] lg:min-h-[600px] relative overflow-hidden ${className}`}>
      {/* Main Mango Container - Better Sizing */}
      <div ref={containerRef} className="flex items-center justify-center perspective-1000 h-full w-full">
        <div className="relative preserve-3d w-fit h-fit">

          {/* � Liquid Morphing Shadow */}
          <div
            ref={shadowRef}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-12 w-48 h-16 opacity-40 blur-lg"
            style={{
              background: 'conic-gradient(from 0deg, rgba(255,140,66,0.6), rgba(255,0,150,0.4), rgba(0,255,255,0.4), rgba(255,140,66,0.6))',
              borderRadius: '50%'
            }}
          ></div>

          {/* 🌈 Rainbow Cycling Glow */}
          <div
            ref={glowRef}
            className="absolute inset-0 rounded-full opacity-40 blur-3xl scale-200"
            style={{
              background: 'conic-gradient(from 0deg, #ff0000, #ff8000, #ffff00, #80ff00, #00ff00, #00ff80, #00ffff, #0080ff, #0000ff, #8000ff, #ff00ff, #ff0080, #ff0000)',
              animation: 'spin 10s linear infinite'
            }}
          ></div>

          {/* 🥭 Mega Tornado Mango */}
          <div
            ref={mangoRef}
            className="relative text-[140px] sm:text-[170px] md:text-[200px] lg:text-[250px] xl:text-[320px] preserve-3d transform-gpu leading-none"
            style={{
              filter: 'drop-shadow(0 30px 60px rgba(255, 140, 66, 0.7)) drop-shadow(0 0 100px rgba(255, 0, 150, 0.5)) drop-shadow(0 0 150px rgba(0, 255, 255, 0.3))',
              textShadow: '0 0 80px rgba(255, 140, 66, 1), 0 0 120px rgba(255, 0, 150, 0.8), 0 0 200px rgba(0, 255, 255, 0.6)',
              background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text'
            }}
          >
            🥭
          </div>

          {/* �️ Tornado Leaves */}
          <div
            ref={leafRef1}
            className="absolute -top-8 -right-6 text-[50px] sm:text-[60px] md:text-[70px] lg:text-[80px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 15px 30px rgba(34, 139, 34, 0.7)) drop-shadow(0 0 50px rgba(0, 255, 0, 0.5))',
              textShadow: '0 0 40px rgba(34, 139, 34, 1), 0 0 80px rgba(0, 255, 0, 0.8), 0 0 120px rgba(50, 255, 50, 0.6)'
            }}
          >
            🌿
          </div>

          <div
            ref={leafRef2}
            className="absolute -top-4 -right-10 text-[35px] sm:text-[45px] md:text-[55px] lg:text-[65px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 12px 24px rgba(34, 139, 34, 0.7)) drop-shadow(0 0 40px rgba(0, 255, 0, 0.5))',
              textShadow: '0 0 35px rgba(34, 139, 34, 1), 0 0 70px rgba(0, 255, 0, 0.8), 0 0 100px rgba(50, 255, 50, 0.6)'
            }}
          >
            🍃
          </div>

          {/* � Vortex Orbiting Elements */}
          <div
            ref={orbitRef1}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div
              className="w-4 h-4 rounded-full shadow-2xl"
              style={{
                transform: 'translateX(150px)',
                background: 'conic-gradient(from 0deg, #ff0080, #8000ff, #0080ff, #ff0080)',
                boxShadow: '0 0 20px rgba(255, 0, 128, 0.8)'
              }}
            ></div>
          </div>

          <div
            ref={orbitRef2}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div
              className="w-6 h-6 rounded-full shadow-2xl"
              style={{
                transform: 'translateX(120px)',
                background: 'conic-gradient(from 90deg, #00ff80, #80ff00, #ffff00, #00ff80)',
                boxShadow: '0 0 25px rgba(0, 255, 128, 0.8)'
              }}
            ></div>
          </div>

          <div
            ref={orbitRef3}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div
              className="w-3 h-3 rounded-full shadow-2xl"
              style={{
                transform: 'translateX(180px)',
                background: 'conic-gradient(from 180deg, #ff8000, #ff0000, #ff0080, #ff8000)',
                boxShadow: '0 0 15px rgba(255, 128, 0, 0.8)'
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* �️ Tornado Particle Vortex */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full opacity-80"
            style={{
              width: `${6 + (i % 4) * 3}px`,
              height: `${6 + (i % 4) * 3}px`,
              background: `conic-gradient(from ${i * 18}deg, ${
                ['#ff0000', '#ff4000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff', '#8000ff', '#ff00ff'][i % 12]
              }, transparent, ${
                ['#ff0000', '#ff4000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff', '#8000ff', '#ff00ff'][i % 12]
              })`,
              left: `${15 + (i * 4) % 70}%`,
              top: `${25 + (i * 7) % 50}%`,
              animation: `float ${2 + (i % 5)}s ease-in-out infinite, spin ${5 + (i % 3)}s linear infinite`,
              animationDelay: `${i * 0.2}s`,
              boxShadow: `0 0 ${10 + (i % 3) * 5}px ${
                ['#ff0000', '#ff4000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff', '#8000ff', '#ff00ff'][i % 12]
              }80`
            }}
          ></div>
        ))}
      </div>

      {/* 🌈 Prismatic Wave Trail */}
      <div className="absolute inset-0 pointer-events-none">
        <div
          className="w-full h-4 absolute top-1/2 transform -translate-y-1/2 opacity-60 blur-sm"
          style={{
            background: 'conic-gradient(from 0deg, #ff0000, #ff8000, #ffff00, #80ff00, #00ff00, #00ff80, #00ffff, #0080ff, #0000ff, #8000ff, #ff00ff, #ff0080, #ff0000)',
            animation: 'spin 8s linear infinite'
          }}
        ></div>
        <div
          className="w-full h-2 absolute top-1/2 transform -translate-y-1/2 opacity-40"
          style={{
            background: 'linear-gradient(90deg, transparent 0%, #ff0080 10%, #8000ff 20%, #0080ff 30%, #00ffff 40%, #00ff80 50%, #80ff00 60%, #ffff00 70%, #ff8000 80%, #ff0000 90%, transparent 100%)',
            animation: 'pulse 3s ease-in-out infinite'
          }}
        ></div>
      </div>

      {/* � Explosive Corner Effects */}
      <div className="absolute top-4 left-4 text-3xl opacity-80" style={{
        animation: 'float 2s ease-in-out infinite, spin 4s linear infinite',
        filter: 'drop-shadow(0 0 10px #ff0080)'
      }}>💥</div>
      <div className="absolute top-4 right-4 text-3xl opacity-80" style={{
        animation: 'float 2.5s ease-in-out infinite, spin 5s linear infinite reverse',
        animationDelay: '0.5s',
        filter: 'drop-shadow(0 0 10px #00ff80)'
      }}>⚡</div>
      <div className="absolute bottom-4 left-4 text-3xl opacity-80" style={{
        animation: 'float 3s ease-in-out infinite, spin 6s linear infinite',
        animationDelay: '1s',
        filter: 'drop-shadow(0 0 10px #0080ff)'
      }}>�️</div>
      <div className="absolute bottom-4 right-4 text-3xl opacity-80" style={{
        animation: 'float 2.2s ease-in-out infinite, spin 4.5s linear infinite reverse',
        animationDelay: '1.5s',
        filter: 'drop-shadow(0 0 10px #ff8000)'
      }}>�</div>
    </div>
  );
}
