'use client';

import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Mango3D({ className = "" }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const mangoRef = useRef<HTMLDivElement>(null);
  const leafRef1 = useRef<HTMLDivElement>(null);
  const leafRef2 = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<HTMLDivElement>(null);
  const thirdElementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const ctx = gsap.context(() => {
      // 1. Main mango continuous 3D rotation
      gsap.to(mangoRef.current, {
        rotationY: 360,
        duration: 8,
        ease: 'none',
        repeat: -1
      });

      // 2. Floating animation
      gsap.to(mangoRef.current, {
        y: -20,
        duration: 3,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });

      // 3. SCROLL-TRIGGERED: 180° Horizontal Rotation on Scroll
      gsap.fromTo(mangoRef.current,
        {
          rotationZ: 0,
          scale: 1
        },
        {
          rotationZ: 180,
          scale: 1.2,
          duration: 2,
          ease: 'power2.inOut',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            scrub: 1,
            toggleActions: 'play reverse play reverse'
          }
        }
      );

      // 4. SCROLL-TRIGGERED: Left to Right Movement
      gsap.fromTo(containerRef.current,
        { x: -200 },
        {
          x: 200,
          duration: 3,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 90%',
            end: 'bottom 10%',
            scrub: 2,
            onUpdate: (self) => {
              // Add wobble effect during movement
              gsap.to(mangoRef.current, {
                rotationX: Math.sin(self.progress * Math.PI * 4) * 15,
                duration: 0.1
              });
            }
          }
        }
      );

      // 5. SCROLL-TRIGGERED: Vertical Down Movement
      gsap.fromTo(containerRef.current,
        { y: -100 },
        {
          y: 100,
          duration: 2,
          ease: 'bounce.out',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 85%',
            end: 'bottom 15%',
            scrub: 1.5
          }
        }
      );

      // 6. Third Element - Special Scroll Animation
      gsap.fromTo(thirdElementRef.current,
        {
          x: -300,
          rotationY: 0,
          opacity: 0
        },
        {
          x: 300,
          rotationY: 180,
          opacity: 1,
          duration: 4,
          ease: 'power3.inOut',
          scrollTrigger: {
            trigger: thirdElementRef.current,
            start: 'top 95%',
            end: 'bottom 5%',
            scrub: 3,
            onStart: () => {
              gsap.to(thirdElementRef.current, {
                scale: 1.5,
                duration: 0.5,
                yoyo: true,
                repeat: 1
              });
            }
          }
        }
      );

      // 7. Leaves scroll-responsive animation
      gsap.to(leafRef1.current, {
        rotation: 360,
        duration: 2,
        scrollTrigger: {
          trigger: containerRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          scrub: 1
        }
      });

      gsap.to(leafRef2.current, {
        rotation: -360,
        duration: 2,
        scrollTrigger: {
          trigger: containerRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          scrub: 1.5
        }
      });

      // 8. Shadow follows movement
      gsap.to(shadowRef.current, {
        scaleX: 2,
        opacity: 0.1,
        scrollTrigger: {
          trigger: containerRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          scrub: 1
        }
      });

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <div className={`w-full h-full relative overflow-hidden ${className}`}>
      {/* Main Mango Container */}
      <div ref={containerRef} className="flex items-center justify-center perspective-1000 h-full">
        <div className="relative preserve-3d">
          {/* Enhanced Shadow with Motion Blur */}
          <div
            ref={shadowRef}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-8 w-32 h-8 bg-black opacity-20 rounded-full blur-sm"
            style={{
              background: 'radial-gradient(ellipse, rgba(0,0,0,0.3) 0%, transparent 70%)'
            }}
          ></div>

          {/* Main 3D Mango */}
          <div
            ref={mangoRef}
            className="relative text-8xl lg:text-9xl preserve-3d transform-gpu"
            style={{
              filter: 'drop-shadow(0 15px 30px rgba(255, 140, 66, 0.4))',
              textShadow: '0 0 40px rgba(255, 140, 66, 0.6), 0 0 80px rgba(255, 140, 66, 0.3)'
            }}
          >
            🥭
          </div>

          {/* Animated Leaves */}
          <div
            ref={leafRef1}
            className="absolute -top-4 -right-2 text-3xl transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 8px 15px rgba(34, 139, 34, 0.4))',
              textShadow: '0 0 20px rgba(34, 139, 34, 0.5)'
            }}
          >
            🌿
          </div>

          <div
            ref={leafRef2}
            className="absolute -top-2 -right-6 text-2xl transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 8px 15px rgba(34, 139, 34, 0.4))',
              textShadow: '0 0 20px rgba(34, 139, 34, 0.5)'
            }}
          >
            🍃
          </div>

          {/* Enhanced Glow Effects */}
          <div className="absolute inset-0 rounded-full bg-gradient-radial from-orange-300/30 to-transparent blur-xl scale-150 animate-pulse"></div>
          <div className="absolute inset-0 rounded-full bg-gradient-radial from-yellow-300/20 to-transparent blur-2xl scale-200"></div>
        </div>
      </div>

      {/* Third Element - Special Scroll-Triggered Mango */}
      <div
        ref={thirdElementRef}
        className="absolute top-1/2 left-0 transform -translate-y-1/2 preserve-3d"
        style={{
          filter: 'drop-shadow(0 10px 20px rgba(255, 140, 66, 0.3))',
          textShadow: '0 0 30px rgba(255, 140, 66, 0.5)'
        }}
      >
        <div className="text-6xl lg:text-7xl transform-gpu preserve-3d">
          🥭
        </div>

        {/* Third Element Glow */}
        <div className="absolute inset-0 rounded-full bg-gradient-radial from-orange-400/40 to-transparent blur-lg scale-150"></div>
      </div>

      {/* Particle Effects */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-orange-300 rounded-full opacity-60 animate-float"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + (i % 3) * 20}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + i * 0.5}s`
            }}
          ></div>
        ))}
      </div>

      {/* Motion Trail Effect */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="w-full h-1 bg-gradient-to-r from-transparent via-orange-300/30 to-transparent absolute top-1/2 transform -translate-y-1/2"></div>
      </div>
    </div>
  );
}
