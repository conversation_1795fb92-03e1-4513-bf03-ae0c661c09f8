'use client';

import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { TextPlugin } from 'gsap/TextPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';

// Register GSAP plugins for 3D elements
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger, TextPlugin, MorphSVGPlugin);
}

export default function Mango3D({ className = "" }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const mangoRef = useRef<HTMLDivElement>(null);
  const leafRef1 = useRef<HTMLDivElement>(null);
  const leafRef2 = useRef<HTMLDivElement>(null);
  const leafRef3 = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<HTMLDivElement>(null);
  const glowRef = useRef<HTMLDivElement>(null);
  const auraRef = useRef<HTMLDivElement>(null);
  const particleRefs = useRef<(HTMLDivElement | null)[]>([]);
  const orbitRef1 = useRef<HTMLDivElement>(null);
  const orbitRef2 = useRef<HTMLDivElement>(null);
  const orbitRef3 = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const ctx = gsap.context(() => {
      // 🥭 ENHANCED ORIENTATION: Perfect 90° down with 3D depth
      gsap.set(mangoRef.current, {
        rotationX: -90,
        transformOrigin: 'center center',
        transformStyle: 'preserve-3d',
        perspective: 1000
      });

      // � BETTER ANIMATION 1: Hypnotic 3D Spiral
      gsap.to(mangoRef.current, {
        rotationY: 360,
        rotationZ: 45,
        duration: 8,
        ease: 'none',
        repeat: -1,
        onUpdate: function() {
          const progress = this.progress();
          const spiral = Math.sin(progress * Math.PI * 6) * 20;
          gsap.set(mangoRef.current, {
            rotationX: -90 + spiral,
            scale: 1 + Math.sin(progress * Math.PI * 4) * 0.1
          });
        }
      });

      // � BETTER ANIMATION 2: Magnetic Levitation
      gsap.to(mangoRef.current, {
        y: -40,
        x: 20,
        duration: 5,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1,
        onUpdate: function() {
          const progress = this.progress();
          const magnetic = Math.cos(progress * Math.PI * 3) * 15;
          gsap.set(mangoRef.current, {
            rotationY: magnetic,
            filter: `brightness(${1 + Math.sin(progress * Math.PI * 2) * 0.2})`
          });
        }
      });

      // 🌪️ BETTER ANIMATION 3: Epic 180° Transformation
      gsap.fromTo(mangoRef.current,
        {
          rotationY: 0,
          scale: 1,
          filter: 'hue-rotate(0deg)'
        },
        {
          rotationY: 180,
          scale: 1.6,
          filter: 'hue-rotate(180deg)',
          duration: 4,
          ease: 'elastic.out(1, 0.5)',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            scrub: 2,
            onEnter: () => {
              gsap.to([glowRef.current, auraRef.current], {
                scale: 3,
                opacity: 0.9,
                duration: 0.8,
                ease: 'expo.out'
              });
            },
            onLeave: () => {
              gsap.to([glowRef.current, auraRef.current], {
                scale: 1,
                opacity: 0.3,
                duration: 0.8
              });
            }
          }
        }
      );

      // 🚀 BETTER ANIMATION 4: Rocket Trajectory
      gsap.fromTo(containerRef.current,
        { x: -300, rotation: -15 },
        {
          x: 300,
          rotation: 15,
          duration: 5,
          ease: 'power3.inOut',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 85%',
            end: 'bottom 15%',
            scrub: 1.5,
            onUpdate: (self) => {
              const trajectory = Math.sin(self.progress * Math.PI * 4) * 25;
              const boost = Math.cos(self.progress * Math.PI * 6) * 10;
              gsap.set(mangoRef.current, {
                rotationZ: trajectory,
                rotationX: -90 + boost,
                scale: 1 + Math.sin(self.progress * Math.PI * 8) * 0.15
              });
            }
          }
        }
      );

      // ⬇️ BETTER ANIMATION 5: Gravity Drop with Bounce
      gsap.fromTo(containerRef.current,
        { y: -120 },
        {
          y: 120,
          duration: 4,
          ease: 'bounce.out',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 90%',
            end: 'bottom 10%',
            scrub: 1,
            onUpdate: (self) => {
              const drop = self.progress * 30;
              const spin = self.progress * 360;
              gsap.set(mangoRef.current, {
                rotationX: -90 + drop,
                rotationY: spin,
                filter: `blur(${self.progress * 2}px) brightness(${1 + self.progress * 0.3})`
              });
            }
          }
        }
      );

      // � BETTER ANIMATION 6: Dynamic Leaf Storm
      gsap.to(leafRef1.current, {
        rotation: 720,
        scale: 1.5,
        x: 30,
        y: -20,
        duration: 6,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        onUpdate: function() {
          const progress = this.progress();
          gsap.set(leafRef1.current, {
            filter: `hue-rotate(${progress * 120}deg) brightness(${1 + progress * 0.5})`
          });
        }
      });

      gsap.to(leafRef2.current, {
        rotation: -540,
        scale: 0.7,
        x: -25,
        y: 25,
        duration: 7,
        ease: 'elastic.inOut(1, 0.3)',
        yoyo: true,
        repeat: -1
      });

      gsap.to(leafRef3.current, {
        rotation: 900,
        scale: 1.3,
        x: 15,
        y: -35,
        duration: 5,
        ease: 'circ.inOut',
        yoyo: true,
        repeat: -1
      });

      // 🌈 BETTER ANIMATION 7: Rainbow Aura
      gsap.to(glowRef.current, {
        scale: 2.5,
        opacity: 0.8,
        duration: 4,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        onUpdate: function() {
          const progress = this.progress();
          const hue = (progress * 360) % 360;
          gsap.set(glowRef.current, {
            filter: `hue-rotate(${hue}deg) brightness(${1.5 + Math.sin(progress * Math.PI * 4) * 0.5})`
          });
        }
      });

      gsap.to(auraRef.current, {
        scale: 3,
        opacity: 0.6,
        rotation: 360,
        duration: 10,
        ease: 'none',
        repeat: -1
      });

      // � BETTER ANIMATION 8: Orbiting Elements
      gsap.to(orbitRef1.current, {
        rotation: 360,
        scale: 1.2,
        duration: 8,
        ease: 'none',
        repeat: -1,
        transformOrigin: '200px 0px'
      });

      gsap.to(orbitRef2.current, {
        rotation: -360,
        scale: 0.8,
        duration: 12,
        ease: 'none',
        repeat: -1,
        transformOrigin: '150px 0px'
      });

      gsap.to(orbitRef3.current, {
        rotation: 360,
        scale: 1.5,
        duration: 6,
        ease: 'none',
        repeat: -1,
        transformOrigin: '250px 0px'
      });

      // 💫 BETTER ANIMATION 9: Particle System
      particleRefs.current.forEach((particle, i) => {
        if (particle) {
          gsap.to(particle, {
            rotation: 360,
            scale: 1 + Math.sin(i) * 0.5,
            duration: 3 + i * 0.5,
            ease: 'none',
            repeat: -1,
            delay: i * 0.2
          });
        }
      });

      // 🌑 BETTER ANIMATION 10: Morphing Shadow
      gsap.to(shadowRef.current, {
        scaleX: 2.5,
        scaleY: 0.5,
        opacity: 0.4,
        skewX: 15,
        duration: 8,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1,
        onUpdate: function() {
          const progress = this.progress();
          const morph = Math.sin(progress * Math.PI * 3) * 20;
          gsap.set(shadowRef.current, {
            borderRadius: `${50 + morph}% ${50 - morph}% ${50 + morph}% ${50 - morph}%`
          });
        }
      });

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <div className={`w-full h-full min-h-[500px] lg:min-h-[700px] relative overflow-hidden ${className}`}>
      {/* Enhanced Main Container */}
      <div ref={containerRef} className="flex items-center justify-center perspective-1000 h-full w-full">
        <div className="relative preserve-3d w-fit h-fit">

          {/* 🌑 Morphing Shadow */}
          <div
            ref={shadowRef}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-16 w-48 h-16 opacity-30 blur-lg"
            style={{
              background: 'conic-gradient(from 0deg, rgba(255,140,66,0.6), rgba(255,0,150,0.4), rgba(0,255,255,0.4), rgba(255,140,66,0.6))',
              borderRadius: '50%'
            }}
          ></div>

          {/* 🌈 Rainbow Aura */}
          <div
            ref={auraRef}
            className="absolute inset-0 rounded-full opacity-40 blur-3xl scale-200"
            style={{
              background: 'conic-gradient(from 0deg, #ff0000, #ff8000, #ffff00, #80ff00, #00ff00, #00ff80, #00ffff, #0080ff, #0000ff, #8000ff, #ff00ff, #ff0080, #ff0000)'
            }}
          ></div>

          {/* 💫 Enhanced Glow */}
          <div
            ref={glowRef}
            className="absolute inset-0 rounded-full opacity-50 blur-2xl scale-175"
            style={{
              background: 'radial-gradient(circle, rgba(255,140,66,0.8) 0%, rgba(255,180,70,0.6) 30%, rgba(255,200,100,0.4) 60%, transparent 100%)'
            }}
          ></div>

          {/* 🥭 ULTIMATE 3D Mango */}
          <div
            ref={mangoRef}
            className="relative text-[140px] sm:text-[170px] md:text-[200px] lg:text-[250px] xl:text-[320px] preserve-3d transform-gpu leading-none"
            style={{
              filter: 'drop-shadow(0 30px 60px rgba(255, 140, 66, 0.7)) drop-shadow(0 0 100px rgba(255, 0, 150, 0.5)) drop-shadow(0 0 150px rgba(0, 255, 255, 0.3))',
              textShadow: '0 0 80px rgba(255, 140, 66, 1), 0 0 120px rgba(255, 0, 150, 0.8), 0 0 200px rgba(0, 255, 255, 0.6)',
              background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text'
            }}
          >
            🥭
          </div>

          {/* � Dynamic Leaf Storm */}
          <div
            ref={leafRef1}
            className="absolute -top-8 -right-6 text-[50px] sm:text-[60px] md:text-[70px] lg:text-[80px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 15px 30px rgba(34, 139, 34, 0.7)) drop-shadow(0 0 50px rgba(0, 255, 0, 0.5))',
              textShadow: '0 0 40px rgba(34, 139, 34, 1), 0 0 80px rgba(0, 255, 0, 0.8), 0 0 120px rgba(50, 255, 50, 0.6)'
            }}
          >
            🌿
          </div>

          <div
            ref={leafRef2}
            className="absolute -top-4 -right-10 text-[35px] sm:text-[45px] md:text-[55px] lg:text-[65px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 12px 24px rgba(34, 139, 34, 0.7)) drop-shadow(0 0 40px rgba(0, 255, 0, 0.5))',
              textShadow: '0 0 35px rgba(34, 139, 34, 1), 0 0 70px rgba(0, 255, 0, 0.8), 0 0 100px rgba(50, 255, 50, 0.6)'
            }}
          >
            �
          </div>

          <div
            ref={leafRef3}
            className="absolute -top-6 -right-2 text-[25px] sm:text-[30px] md:text-[35px] lg:text-[40px] transform-gpu preserve-3d"
            style={{
              filter: 'drop-shadow(0 8px 16px rgba(34, 139, 34, 0.7)) drop-shadow(0 0 30px rgba(0, 255, 0, 0.5))',
              textShadow: '0 0 25px rgba(34, 139, 34, 1), 0 0 50px rgba(0, 255, 0, 0.8), 0 0 80px rgba(50, 255, 50, 0.6)'
            }}
          >
            �
          </div>

          {/* 🌀 Orbiting Elements */}
          <div
            ref={orbitRef1}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div
              className="w-6 h-6 rounded-full shadow-2xl"
              style={{
                transform: 'translateX(200px)',
                background: 'conic-gradient(from 0deg, #ff0080, #8000ff, #0080ff, #ff0080)',
                boxShadow: '0 0 30px rgba(255, 0, 128, 0.8)'
              }}
            ></div>
          </div>

          <div
            ref={orbitRef2}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div
              className="w-8 h-8 rounded-full shadow-2xl"
              style={{
                transform: 'translateX(150px)',
                background: 'conic-gradient(from 90deg, #00ff80, #80ff00, #ffff00, #00ff80)',
                boxShadow: '0 0 35px rgba(0, 255, 128, 0.8)'
              }}
            ></div>
          </div>

          <div
            ref={orbitRef3}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div
              className="w-4 h-4 rounded-full shadow-2xl"
              style={{
                transform: 'translateX(250px)',
                background: 'conic-gradient(from 180deg, #ff8000, #ff0000, #ff0080, #ff8000)',
                boxShadow: '0 0 25px rgba(255, 128, 0, 0.8)'
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* 🌪️ Enhanced Particle Vortex */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            ref={(el) => { particleRefs.current[i] = el; }}
            className="absolute rounded-full opacity-80"
            style={{
              width: `${6 + (i % 4) * 3}px`,
              height: `${6 + (i % 4) * 3}px`,
              background: `conic-gradient(from ${i * 24}deg, ${
                ['#ff0000', '#ff4000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff', '#8000ff', '#ff00ff'][i % 12]
              }, transparent, ${
                ['#ff0000', '#ff4000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff', '#8000ff', '#ff00ff'][i % 12]
              })`,
              left: `${15 + (i * 5) % 70}%`,
              top: `${25 + (i * 7) % 50}%`,
              boxShadow: `0 0 ${15 + (i % 3) * 5}px ${
                ['#ff0000', '#ff4000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff', '#8000ff', '#ff00ff'][i % 12]
              }80`
            }}
          ></div>
        ))}
      </div>

      {/* 🔥 Epic Corner Effects */}
      <div className="absolute top-6 left-6 text-4xl opacity-90" style={{
        animation: 'float 2s ease-in-out infinite, vortex 8s linear infinite',
        filter: 'drop-shadow(0 0 15px #ff0080)'
      }}>🔥</div>
      <div className="absolute top-6 right-6 text-4xl opacity-90" style={{
        animation: 'float 2.5s ease-in-out infinite, vortex 10s linear infinite reverse',
        animationDelay: '0.5s',
        filter: 'drop-shadow(0 0 15px #00ff80)'
      }}>⚡</div>
      <div className="absolute bottom-6 left-6 text-4xl opacity-90" style={{
        animation: 'float 3s ease-in-out infinite, vortex 12s linear infinite',
        animationDelay: '1s',
        filter: 'drop-shadow(0 0 15px #0080ff)'
      }}>�️</div>
      <div className="absolute bottom-6 right-6 text-4xl opacity-90" style={{
        animation: 'float 2.2s ease-in-out infinite, vortex 9s linear infinite reverse',
        animationDelay: '1.5s',
        filter: 'drop-shadow(0 0 15px #ff8000)'
      }}>�</div>
    </div>
  );
}
