'use client';

import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Mango3D from './Mango3D';

if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function MangoShowcase() {
  const showcaseRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!showcaseRef.current) return;

    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo('.showcase-title',
        { opacity: 0, y: 100, scale: 0.8 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.5,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: '.showcase-title',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Description animation
      gsap.fromTo('.showcase-description',
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.showcase-description',
            start: 'top 85%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Feature cards animation
      gsap.fromTo('.feature-card',
        { opacity: 0, x: -100, rotationY: -45 },
        {
          opacity: 1,
          x: 0,
          rotationY: 0,
          duration: 1,
          stagger: 0.2,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.features-container',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

    }, showcaseRef);

    return () => ctx.revert();
  }, []);

  return (
    <section ref={showcaseRef} className="py-20 bg-gradient-to-b from-orange-50 via-yellow-50 to-green-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-64 h-64 bg-orange-300 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-48 h-48 bg-green-300 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-yellow-300 rounded-full blur-3xl opacity-50"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="showcase-title text-4xl lg:text-6xl font-bold mb-6">
            <span className="text-transparent bg-clip-text mango-gradient">🥭 Elegant 3D Mango</span>
            <span className="text-green-700"> Simple & Beautiful</span>
          </h2>
          <p className="showcase-description text-xl text-gray-600 max-w-4xl mx-auto">
            Experience the perfectly oriented 3D mango with clean gradients, smooth 180° rotation,
            gentle movements, and elegant simplicity that focuses on natural beauty!
          </p>
        </div>

        {/* Main 3D Mango Display */}
        <div className="mb-20">
          <div className="h-96 lg:h-[600px] relative">
            <Mango3D className="w-full h-full" />
          </div>
        </div>

        {/* Animation Features */}
        <div className="features-container grid md:grid-cols-3 gap-8 mb-16">
          <div className="feature-card bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl preserve-3d">
            <div className="text-center">
              <div className="text-5xl mb-4">🔄</div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Smooth Rotation</h3>
              <p className="text-gray-600">
                Gentle 180° horizontal rotation with elegant scaling from 1x to 1.3x,
                using clean power2.inOut easing for natural movement.
              </p>
            </div>
          </div>

          <div className="feature-card bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl preserve-3d">
            <div className="text-center">
              <div className="text-5xl mb-4">↔️</div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Gentle Movement</h3>
              <p className="text-gray-600">
                Soft horizontal movement from -150px to +150px with smooth
                transitions and refined power2.inOut easing.
              </p>
            </div>
          </div>

          <div className="feature-card bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl preserve-3d">
            <div className="text-center">
              <div className="text-5xl mb-4">⬇️</div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Elegant Descent</h3>
              <p className="text-gray-600">
                Controlled vertical movement with perfect 90° down orientation,
                maintaining elegance and simplicity throughout.
              </p>
            </div>
          </div>
        </div>

        {/* Technical Details */}
        <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl">
          <h3 className="text-3xl font-bold text-center text-gray-800 mb-8">
            Advanced GSAP Features
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl mb-3">🎯</div>
              <h4 className="font-semibold text-gray-800 mb-2">Perfect Orientation</h4>
              <p className="text-sm text-gray-600">90° down facing mango</p>
            </div>

            <div className="text-center">
              <div className="text-3xl mb-3">🌊</div>
              <h4 className="font-semibold text-gray-800 mb-2">Smooth Easing</h4>
              <p className="text-sm text-gray-600">Power2/3 and sine curves</p>
            </div>

            <div className="text-center">
              <div className="text-3xl mb-3">💫</div>
              <h4 className="font-semibold text-gray-800 mb-2">Elegant Glow</h4>
              <p className="text-sm text-gray-600">Radial gradient effects</p>
            </div>

            <div className="text-center">
              <div className="text-3xl mb-3">✨</div>
              <h4 className="font-semibold text-gray-800 mb-2">Gentle Particles</h4>
              <p className="text-sm text-gray-600">8 floating elements</p>
            </div>
          </div>
        </div>

        {/* Scroll Instruction */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-2 bg-orange-100 px-6 py-3 rounded-full">
            <span className="text-orange-600 font-semibold">Scroll down to see the magic! ✨</span>
            <div className="animate-bounce">⬇️</div>
          </div>
        </div>
      </div>
    </section>
  );
}
