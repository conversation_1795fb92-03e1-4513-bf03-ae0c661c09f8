'use client';

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';

const mangoVarieties = [
  {
    name: '<PERSON><PERSON><PERSON>',
    description: 'The king of mangoes with rich, creamy texture and sweet aroma.',
    color: '#FFD700',
    season: 'April - June',
    features: ['Premium Quality', 'Export Grade', 'Sweet & Aromatic']
  },
  {
    name: '<PERSON><PERSON>',
    description: 'Golden yellow mango with saffron-like aroma and taste.',
    color: '#FFA500',
    season: 'May - July',
    features: ['Saffron Aroma', 'Juicy Pulp', 'Rich Flavor']
  },
  {
    name: '<PERSON><PERSON>',
    description: 'Elongated mango with sweet taste and minimal fiber.',
    color: '#FFFF99',
    season: 'June - August',
    features: ['Fiber-free', 'Sweet Taste', 'Long Shelf Life']
  },
  {
    name: '<PERSON>ra',
    description: 'Green-skinned mango with incredibly sweet and juicy flesh.',
    color: '#90EE90',
    season: 'July - August',
    features: ['Unique Green Skin', 'Super Sweet', 'Traditional Variety']
  }
];

export default function ProductsSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [selectedMango, setSelectedMango] = useState(0);

  useEffect(() => {
    if (!sectionRef.current) return;

    const ctx = gsap.context(() => {
      // Animate product cards
      gsap.fromTo('.product-card',
        { 
          opacity: 0, 
          y: 100,
          rotateY: -15
        },
        {
          opacity: 1,
          y: 0,
          rotateY: 0,
          duration: 1,
          stagger: 0.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: '.products-grid',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Floating animation for mango icons
      gsap.to('.mango-icon', {
        y: -10,
        rotation: 5,
        duration: 2,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.3
      });

    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const handleMangoSelect = (index: number) => {
    setSelectedMango(index);
    
    // Animate selection
    gsap.fromTo('.selected-mango-details',
      { opacity: 0, x: 50 },
      { opacity: 1, x: 0, duration: 0.5, ease: 'power2.out' }
    );
  };

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-b from-orange-50 to-yellow-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 text-9xl">🥭</div>
        <div className="absolute top-32 right-20 text-7xl">🌿</div>
        <div className="absolute bottom-20 left-1/4 text-8xl">🥭</div>
        <div className="absolute bottom-10 right-10 text-6xl">🌱</div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 gsap-fade-in">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            <span className="text-transparent bg-clip-text mango-gradient">Premium Mango</span>
            <span className="text-green-700"> Varieties</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover our carefully cultivated collection of premium mango varieties, 
            each with its unique flavor profile and characteristics.
          </p>
        </div>

        {/* Interactive Mango Selector */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Mango Varieties Grid */}
          <div className="products-grid grid grid-cols-2 gap-6">
            {mangoVarieties.map((mango, index) => (
              <div 
                key={index}
                className={`product-card cursor-pointer transition-all duration-300 ${
                  selectedMango === index ? 'scale-105' : 'hover:scale-102'
                }`}
                onClick={() => handleMangoSelect(index)}
              >
                <div className={`bg-white rounded-2xl p-6 shadow-lg preserve-3d ${
                  selectedMango === index ? 'ring-4 ring-orange-300 shadow-2xl' : ''
                }`}>
                  <div 
                    className="mango-icon text-6xl mb-4 text-center"
                    style={{ filter: `hue-rotate(${index * 30}deg)` }}
                  >
                    🥭
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 text-center mb-2">
                    {mango.name}
                  </h3>
                  <p className="text-sm text-gray-600 text-center mb-3">
                    {mango.season}
                  </p>
                  <div className="flex justify-center">
                    <div 
                      className="w-8 h-8 rounded-full border-2 border-white shadow-md"
                      style={{ backgroundColor: mango.color }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Selected Mango Details */}
          <div className="selected-mango-details">
            <div className="bg-white rounded-3xl p-8 shadow-xl">
              <div className="text-center mb-6">
                <div className="text-8xl mb-4">🥭</div>
                <h3 className="text-3xl font-bold text-gray-800 mb-2">
                  {mangoVarieties[selectedMango].name}
                </h3>
                <div 
                  className="inline-block px-4 py-2 rounded-full text-white font-semibold"
                  style={{ backgroundColor: mangoVarieties[selectedMango].color }}
                >
                  {mangoVarieties[selectedMango].season}
                </div>
              </div>
              
              <p className="text-gray-600 text-center mb-6 text-lg leading-relaxed">
                {mangoVarieties[selectedMango].description}
              </p>
              
              <div className="space-y-3 mb-8">
                <h4 className="font-semibold text-gray-800 text-center">Key Features:</h4>
                {mangoVarieties[selectedMango].features.map((feature, idx) => (
                  <div key={idx} className="flex items-center justify-center">
                    <span className="text-green-500 mr-2">✓</span>
                    <span className="text-gray-600">{feature}</span>
                  </div>
                ))}
              </div>
              
              <div className="text-center">
                <button className="mango-gradient text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                  Order Now
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Quality Assurance */}
        <div className="bg-white rounded-3xl p-8 shadow-xl gsap-fade-in">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-gray-800 mb-6">
              Our Quality Promise
            </h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl mb-4">🌱</div>
                <h4 className="font-semibold text-gray-800 mb-2">100% Organic</h4>
                <p className="text-gray-600">No chemicals or pesticides used</p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">🏆</div>
                <h4 className="font-semibold text-gray-800 mb-2">Premium Grade</h4>
                <p className="text-gray-600">Hand-picked at perfect ripeness</p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">📦</div>
                <h4 className="font-semibold text-gray-800 mb-2">Fresh Delivery</h4>
                <p className="text-gray-600">Farm to table in 24-48 hours</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
