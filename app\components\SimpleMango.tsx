'use client';

import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

export default function SimpleMango({ className = "" }: { className?: string }) {
  const mangoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mangoRef.current) return;

    const ctx = gsap.context(() => {
      gsap.to(mangoRef.current, {
        rotation: 360,
        duration: 8,
        ease: 'none',
        repeat: -1
      });

      gsap.to(mangoRef.current, {
        y: -20,
        duration: 3,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
      });
    }, mangoRef);

    return () => ctx.revert();
  }, []);

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div 
        ref={mangoRef}
        className="text-8xl lg:text-9xl filter drop-shadow-lg"
        style={{
          textShadow: '0 0 20px rgba(255, 140, 66, 0.5)'
        }}
      >
        🥭
      </div>
    </div>
  );
}
