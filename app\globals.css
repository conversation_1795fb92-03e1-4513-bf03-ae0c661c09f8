@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Mango Farm Color Palette */
  --mango-primary: #FF8C42;
  --mango-secondary: #FFB347;
  --mango-accent: #FFA500;
  --green-primary: #228B22;
  --green-secondary: #32CD32;
  --earth-brown: #8B4513;
  --sky-blue: #87CEEB;
  --cream: #FFF8DC;

  /* Text Colors */
  --foreground-rgb: 51, 51, 51;
  --background-start-rgb: 255, 248, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 20, 30, 20;
    --background-end-rgb: 10, 20, 10;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      135deg,
      rgba(255, 248, 220, 0.8) 0%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(135, 206, 235, 0.1) 100%
    );
  font-family: var(--font-poppins), system-ui, sans-serif;
  overflow-x: hidden;
}

/* Custom GSAP Animation Classes */
.gsap-fade-in {
  opacity: 0;
  transform: translateY(50px);
}

.gsap-scale-in {
  opacity: 0;
  transform: scale(0.8);
}

.gsap-slide-left {
  opacity: 0;
  transform: translateX(-100px);
}

.gsap-slide-right {
  opacity: 0;
  transform: translateX(100px);
}

/* 3D Transform Utilities */
.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

/* Mango Theme Components */
.mango-gradient {
  background: linear-gradient(135deg, var(--mango-primary), var(--mango-secondary));
}

.green-gradient {
  background: linear-gradient(135deg, var(--green-primary), var(--green-secondary));
}

.earth-gradient {
  background: linear-gradient(135deg, var(--earth-brown), #CD853F);
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(255, 140, 66, 0.3); }
  50% { box-shadow: 0 0 40px rgba(255, 140, 66, 0.6); }
}

.pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  .rotate-x-12 {
    transform: rotateX(12deg);
  }
}
