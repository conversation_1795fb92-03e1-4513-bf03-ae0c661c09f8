@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Mango Farm Color Palette */
  --mango-primary: #FF8C42;
  --mango-secondary: #FFB347;
  --mango-accent: #FFA500;
  --green-primary: #228B22;
  --green-secondary: #32CD32;
  --earth-brown: #8B4513;
  --sky-blue: #87CEEB;
  --cream: #FFF8DC;

  /* Text Colors */
  --foreground-rgb: 51, 51, 51;
  --background-start-rgb: 255, 248, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 20, 30, 20;
    --background-end-rgb: 10, 20, 10;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      135deg,
      rgba(255, 248, 220, 0.8) 0%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(135, 206, 235, 0.1) 100%
    );
  font-family: var(--font-poppins), system-ui, sans-serif;
  overflow-x: hidden;
}

/* Custom GSAP Animation Classes */
.gsap-fade-in {
  opacity: 0;
  transform: translateY(50px);
}

.gsap-scale-in {
  opacity: 0;
  transform: scale(0.8);
}

.gsap-slide-left {
  opacity: 0;
  transform: translateX(-100px);
}

.gsap-slide-right {
  opacity: 0;
  transform: translateX(100px);
}

/* 3D Transform Utilities */
.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

/* Mango Theme Components */
.mango-gradient {
  background: linear-gradient(135deg, var(--mango-primary), var(--mango-secondary));
}

.green-gradient {
  background: linear-gradient(135deg, var(--green-primary), var(--green-secondary));
}

.earth-gradient {
  background: linear-gradient(135deg, var(--earth-brown), #CD853F);
}

/* Enhanced Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  25% { transform: translateY(-15px) rotate(2deg) scale(1.05); }
  50% { transform: translateY(-30px) rotate(5deg) scale(1.1); }
  75% { transform: translateY(-15px) rotate(3deg) scale(1.05); }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Spin Animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced Pulse Animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 140, 66, 0.3);
    filter: brightness(1);
  }
  50% {
    box-shadow: 0 0 60px rgba(255, 140, 66, 0.8), 0 0 100px rgba(255, 0, 150, 0.4);
    filter: brightness(1.3);
  }
}

.pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

/* Tornado Animation */
@keyframes tornado {
  0% { transform: rotate(0deg) scale(1) skew(0deg); }
  25% { transform: rotate(90deg) scale(1.1) skew(5deg); }
  50% { transform: rotate(180deg) scale(1.2) skew(10deg); }
  75% { transform: rotate(270deg) scale(1.1) skew(5deg); }
  100% { transform: rotate(360deg) scale(1) skew(0deg); }
}

/* Vortex Animation */
@keyframes vortex {
  0% { transform: rotate(0deg) scale(1); filter: hue-rotate(0deg); }
  50% { transform: rotate(180deg) scale(1.5); filter: hue-rotate(180deg); }
  100% { transform: rotate(360deg) scale(1); filter: hue-rotate(360deg); }
}

/* Lightning Animation */
@keyframes lightning {
  0%, 100% { transform: translateX(0) skew(0deg); filter: brightness(1); }
  10% { transform: translateX(-5px) skew(-2deg); filter: brightness(1.5); }
  20% { transform: translateX(3px) skew(1deg); filter: brightness(1.2); }
  30% { transform: translateX(-2px) skew(-1deg); filter: brightness(1.8); }
  40% { transform: translateX(4px) skew(2deg); filter: brightness(1.1); }
  50% { transform: translateX(-3px) skew(-1.5deg); filter: brightness(2); }
  60% { transform: translateX(2px) skew(1deg); filter: brightness(1.3); }
  70% { transform: translateX(-1px) skew(-0.5deg); filter: brightness(1.6); }
  80% { transform: translateX(1px) skew(0.5deg); filter: brightness(1.4); }
  90% { transform: translateX(-1px) skew(-0.2deg); filter: brightness(1.7); }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  .rotate-x-12 {
    transform: rotateX(12deg);
  }
}
