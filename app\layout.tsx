import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });
const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins"
});

export const metadata: Metadata = {
  title: "Buppi Mango & Agri Farm - Premium Organic Mangoes",
  description: "Experience the finest organic mangoes from Buppi Mango & Agri Farm. Sustainable farming, premium quality fruits, and authentic agricultural practices.",
  keywords: "mango farm, organic mangoes, agriculture, sustainable farming, premium fruits, Buppi farm",
  authors: [{ name: "Buppi Mango & Agri Farm" }],
  openGraph: {
    title: "Buppi Mango & Agri Farm - Premium Organic Mangoes",
    description: "Experience the finest organic mangoes from our sustainable farm",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} ${poppins.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
