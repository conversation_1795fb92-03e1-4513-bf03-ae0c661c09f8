'use client';

import { useEffect } from 'react';
import { useGSAPAnimation, useParallax, useTextAnimation } from './hooks/useGSAP';
import HeroSection from './components/HeroSection';
import MangoShowcase from './components/MangoShowcase';
import AboutSection from './components/AboutSection';
import ProductsSection from './components/ProductsSection';
import ContactSection from './components/ContactSection';

export default function Home() {
  const containerRef = useGSAPAnimation();

  useParallax();
  useTextAnimation();

  useEffect(() => {
    // Smooth scroll behavior
    const handleSmoothScroll = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.hash) {
        e.preventDefault();
        const element = document.querySelector(target.hash);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }
    };

    document.addEventListener('click', handleSmoothScroll);
    return () => document.removeEventListener('click', handleSmoothScroll);
  }, []);

  return (
    <main ref={containerRef} className="min-h-screen">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md shadow-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🥭</span>
              <span className="text-xl font-bold text-gray-800">Buppi Mango Farm</span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#home" className="text-gray-600 hover:text-orange-500 transition-colors">Home</a>
              <a href="#showcase" className="text-gray-600 hover:text-orange-500 transition-colors">3D Demo</a>
              <a href="#about" className="text-gray-600 hover:text-orange-500 transition-colors">About</a>
              <a href="#products" className="text-gray-600 hover:text-orange-500 transition-colors">Products</a>
              <a href="#contact" className="text-gray-600 hover:text-orange-500 transition-colors">Contact</a>
            </div>
            <button className="mango-gradient text-white px-6 py-2 rounded-full font-semibold hover:shadow-lg transition-all duration-300">
              Order Now
            </button>
          </div>
        </div>
      </nav>

      {/* Page Sections */}
      <section id="home">
        <HeroSection />
      </section>

      <section id="showcase">
        <MangoShowcase />
      </section>

      <section id="about">
        <AboutSection />
      </section>

      <section id="products">
        <ProductsSection />
      </section>

      <section id="contact">
        <ContactSection />
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-12">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">🥭</span>
                <span className="text-xl font-bold">Buppi Mango Farm</span>
              </div>
              <p className="text-gray-400">
                Growing the finest organic mangoes with sustainable farming practices for over 25 years.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#home" className="hover:text-orange-400 transition-colors">Home</a></li>

                <li><a href="#about" className="hover:text-orange-400 transition-colors">About Us</a></li>
                <li><a href="#products" className="hover:text-orange-400 transition-colors">Products</a></li>
                <li><a href="#contact" className="hover:text-orange-400 transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Products</h3>
              <ul className="space-y-2 text-gray-400">
                <li>Alphonso Mangoes</li>
                <li>Kesar Mangoes</li>
                <li>Dasheri Mangoes</li>
                <li>Langra Mangoes</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Contact Info</h3>
              <div className="space-y-2 text-gray-400">
                <p>📍 Buppi Village, Agricultural District</p>
                <p>📞 +91 98765 43210</p>
                <p>✉️ <EMAIL></p>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Buppi Mango & Agri Farm. All rights reserved. Made with 🥭 and ❤️</p>
          </div>
        </div>
      </footer>
    </main>
  );
}
