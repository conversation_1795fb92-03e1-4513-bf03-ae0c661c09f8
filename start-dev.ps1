# Buppi Mango Farm Website Startup Script
Write-Host "🥭 Starting Buppi Mango Farm Website..." -ForegroundColor Green
Write-Host ""

# Set the correct directory
$projectPath = "E:\code\git\Frontend\Buppi Mango & Agri Farm\mango"
Set-Location $projectPath

Write-Host "📁 Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Check if node_modules exists
if (!(Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Cyan
    npm install
    Write-Host ""
}

Write-Host "🚀 Starting development server..." -ForegroundColor Green
Write-Host "🌐 Website will be available at: http://localhost:3000" -ForegroundColor Magenta
Write-Host ""

# Start the development server
npm run dev
