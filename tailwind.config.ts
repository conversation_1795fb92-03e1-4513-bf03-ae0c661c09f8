import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'poppins': ['var(--font-poppins)', 'sans-serif'],
      },
      colors: {
        mango: {
          primary: '#FF8C42',
          secondary: '#FFB347',
          accent: '#FFA500',
        },
        green: {
          primary: '#228B22',
          secondary: '#32CD32',
        },
        earth: {
          brown: '#8B4513',
        },
        sky: {
          blue: '#87CEEB',
        },
        cream: '#FFF8DC',
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-glow': 'pulse-glow 3s ease-in-out infinite',
        'spin': 'spin 1s linear infinite',
        'tornado': 'tornado 4s ease-in-out infinite',
        'vortex': 'vortex 6s linear infinite',
        'lightning': 'lightning 0.5s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px) rotate(0deg) scale(1)' },
          '25%': { transform: 'translateY(-15px) rotate(2deg) scale(1.05)' },
          '50%': { transform: 'translateY(-30px) rotate(5deg) scale(1.1)' },
          '75%': { transform: 'translateY(-15px) rotate(3deg) scale(1.05)' },
        },
        'pulse-glow': {
          '0%, 100%': {
            boxShadow: '0 0 20px rgba(255, 140, 66, 0.3)',
            filter: 'brightness(1)'
          },
          '50%': {
            boxShadow: '0 0 60px rgba(255, 140, 66, 0.8), 0 0 100px rgba(255, 0, 150, 0.4)',
            filter: 'brightness(1.3)'
          },
        },
        spin: {
          'from': { transform: 'rotate(0deg)' },
          'to': { transform: 'rotate(360deg)' },
        },
        tornado: {
          '0%': { transform: 'rotate(0deg) scale(1) skew(0deg)' },
          '25%': { transform: 'rotate(90deg) scale(1.1) skew(5deg)' },
          '50%': { transform: 'rotate(180deg) scale(1.2) skew(10deg)' },
          '75%': { transform: 'rotate(270deg) scale(1.1) skew(5deg)' },
          '100%': { transform: 'rotate(360deg) scale(1) skew(0deg)' },
        },
        vortex: {
          '0%': { transform: 'rotate(0deg) scale(1)', filter: 'hue-rotate(0deg)' },
          '50%': { transform: 'rotate(180deg) scale(1.5)', filter: 'hue-rotate(180deg)' },
          '100%': { transform: 'rotate(360deg) scale(1)', filter: 'hue-rotate(360deg)' },
        },
        lightning: {
          '0%, 100%': { transform: 'translateX(0) skew(0deg)', filter: 'brightness(1)' },
          '10%': { transform: 'translateX(-5px) skew(-2deg)', filter: 'brightness(1.5)' },
          '20%': { transform: 'translateX(3px) skew(1deg)', filter: 'brightness(1.2)' },
          '30%': { transform: 'translateX(-2px) skew(-1deg)', filter: 'brightness(1.8)' },
          '40%': { transform: 'translateX(4px) skew(2deg)', filter: 'brightness(1.1)' },
          '50%': { transform: 'translateX(-3px) skew(-1.5deg)', filter: 'brightness(2)' },
          '60%': { transform: 'translateX(2px) skew(1deg)', filter: 'brightness(1.3)' },
          '70%': { transform: 'translateX(-1px) skew(-0.5deg)', filter: 'brightness(1.6)' },
          '80%': { transform: 'translateX(1px) skew(0.5deg)', filter: 'brightness(1.4)' },
          '90%': { transform: 'translateX(-1px) skew(-0.2deg)', filter: 'brightness(1.7)' },
        },
      },
    },
  },
  plugins: [],
};
export default config;
