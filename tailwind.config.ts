import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'poppins': ['var(--font-poppins)', 'sans-serif'],
      },
      colors: {
        mango: {
          primary: '#FF8C42',
          secondary: '#FFB347',
          accent: '#FFA500',
        },
        green: {
          primary: '#228B22',
          secondary: '#32CD32',
        },
        earth: {
          brown: '#8B4513',
        },
        sky: {
          blue: '#87CEEB',
        },
        cream: '#FFF8DC',
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-glow': 'pulse-glow 3s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
          '50%': { transform: 'translateY(-20px) rotate(5deg)' },
        },
        'pulse-glow': {
          '0%, 100%': { boxShadow: '0 0 20px rgba(255, 140, 66, 0.3)' },
          '50%': { boxShadow: '0 0 40px rgba(255, 140, 66, 0.6)' },
        },
      },
    },
  },
  plugins: [],
};
export default config;
